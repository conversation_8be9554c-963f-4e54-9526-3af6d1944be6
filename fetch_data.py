import requests
import json
import os
import traceback
from difflib import SequenceMatcher
from template import create_brochure

# WooCommerce API credentials
base_url = "https://lavenderblush-aardvark-518657.hostingersite.com/wp-json/wc/v3"
consumer_key = "ck_be6264af2dc032603285baf41699d2858d93d5bb"
consumer_secret = "cs_e8eac9171c01f00bb2c84a363dc54da15331771f"

def get_meta_value(meta_data, key):
    """Extract a specific meta value from the product's meta_data"""
    for meta in meta_data:
        if meta['key'] == key:
            return meta['value']
    return ""

def fuzzy_match_category_to_image(category_name, available_images, threshold=0.6):
    """
    Match category names to available image files using fuzzy matching.
    Returns the best matching image filename or None if no good match found.
    """
    if not category_name or not available_images:
        return None

    # Clean category name for matching
    clean_category = category_name.lower().replace(" ", "-").replace("_", "-")

    best_match = None
    best_ratio = 0

    for image_file in available_images:
        # Remove extension and clean filename
        image_name = os.path.splitext(image_file)[0].lower()

        # Calculate similarity ratio
        ratio = SequenceMatcher(None, clean_category, image_name).ratio()

        if ratio > best_ratio and ratio >= threshold:
            best_ratio = ratio
            best_match = image_file

    return best_match

def get_category_mappings():
    """
    Define mappings between category names (Arabic/English) and image files.
    This helps with consistent category-to-image matching.
    """
    return {
        # Arabic categories
        "سناكس": "snacks-.jpg",
        "الفواكه المجففة": "dried-fruits.jpg",
        "القهوة": "coffee.jpg",
        "الحلويات": "confectionery.jpg",
        "البقوليات": "pulses.jpg",
        "المكسرات والبذور": "nuts-and-seeds.jpg",
        "الحلوى المطاطية": "gummies.jpg",
        "معبأ": "pre-packed-snacks.jpg",
        "سائب": "loose-item-snacks.jpg",

        # English categories
        "snacks": "snacks-.jpg",
        "dried fruits": "dried-fruits.jpg",
        "coffee": "coffee.jpg",
        "confectionery": "confectionery.jpg",
        "pulses": "pulses.jpg",
        "nuts & seeds": "nuts-and-seeds.jpg",
        "nuts and seeds": "nuts-and-seeds.jpg",
        "gummies": "gummies.jpg",
        "pre-packed": "pre-packed-snacks.jpg",
        "prepacked": "pre-packed-snacks.jpg",
        "loose": "loose-item-snacks.jpg",
        "loose item": "loose-item-snacks.jpg"
    }

def get_category_translations():
    """
    Define mappings between Arabic and English category names.
    This ensures consistent category grouping across languages.
    """
    return {
        # Arabic to English mappings
        "سناكس": "Snacks",
        "الفواكه المجففة": "Dried Fruits",
        "القهوة": "Coffee",
        "الحلويات": "Confectionery",
        "البقوليات": "Pulses",
        "المكسرات والبذور": "Nuts & Seeds",
        "الحلوى المطاطية": "Gummies",
        "معبأ": "Pre-packed",
        "سائب": "Loose Item",
        "الجيلي والسكاكر": "Gummies",
        "سائبة بالوزن": "Loose Item",

        # English to Arabic mappings (reverse)
        "snacks": "سناكس",
        "dried fruits": "الفواكه المجففة",
        "coffee": "القهوة",
        "confectionery": "الحلويات",
        "pulses": "البقوليات",
        "nuts & seeds": "المكسرات والبذور",
        "nuts and seeds": "المكسرات والبذور",
        "gummies": "الحلوى المطاطية",
        "pre-packed": "معبأ",
        "prepacked": "معبأ",
        "loose": "سائب",
        "loose item": "سائب"
    }

def translate_category_to_language(category_name, target_language):
    """
    Translate a category name to the target language.

    Args:
        category_name (str): The category name to translate
        target_language (str): Target language ("arabic" or "english")

    Returns:
        str: Translated category name, or original if no translation found
    """
    if not category_name:
        return category_name

    translations = get_category_translations()
    category_lower = category_name.lower()

    # Define known English categories (both lowercase and proper case)
    english_categories = ["snacks", "dried fruits", "coffee", "confectionery", "pulses", "nuts & seeds", "nuts and seeds", "gummies", "pre-packed", "prepacked", "loose", "loose item"]
    arabic_categories = ["سناكس", "الفواكه المجففة", "القهوة", "الحلويات", "البقوليات", "المكسرات والبذور", "الحلوى المطاطية", "معبأ", "سائب", "الجيلي والسكاكر", "سائبة بالوزن"]

    if target_language.lower() == "english":
        # If it's Arabic, translate to English
        if category_name in arabic_categories:
            return translations.get(category_name, category_name)
        # If it's already in English (check both lowercase and original), return proper case
        elif category_lower in english_categories:
            # Map common English categories to their proper case
            proper_case_map = {
                "snacks": "Snacks",
                "dried fruits": "Dried Fruits",
                "coffee": "Coffee",
                "confectionery": "Confectionery",
                "pulses": "Pulses",
                "nuts & seeds": "Nuts & Seeds",
                "nuts and seeds": "Nuts & Seeds",
                "gummies": "Gummies",
                "pre-packed": "Pre-packed",
                "prepacked": "Pre-packed",
                "loose": "Loose",
                "loose item": "Loose Item"
            }
            return proper_case_map.get(category_lower, category_name.title())
        else:
            # Unknown category, return as-is
            return category_name
    else:  # Arabic
        # If it's already in Arabic, return as-is
        if category_name in arabic_categories:
            return category_name
        # If it's English (check lowercase), translate to Arabic
        elif category_lower in english_categories:
            return translations.get(category_lower, category_name)
        else:
            # Unknown category, return as-is
            return category_name

def fetch_and_process_products(language="arabic"):
    """
    Fetch products from WooCommerce API and process them for the brochure.

    Args:
        language (str): Language for the brochure - "arabic" or "english"
    """
    try:
        # Initialize variables for pagination
        page = 1
        per_page = 100  # Maximum allowed by WooCommerce API
        all_products = []
        total_pages = None

        print("Fetching products from WooCommerce API...")

        # Loop through all pages of products
        while True:
            print(f"Fetching page {page}...")

            # Fetch products for the current page
            response = requests.get(
                f"{base_url}/products",
                auth=(consumer_key, consumer_secret),
                params={
                    "per_page": per_page,
                    "page": page
                }
            )

            if response.status_code == 200:
                # Get products from the current page
                page_products = response.json()

                # If this is the first page, get the total number of pages
                if page == 1:
                    # WooCommerce includes total pages in the headers
                    total_pages = int(response.headers.get('X-WP-TotalPages', 1))
                    print(f"Found {total_pages} pages of products")

                # Add products from this page to our collection
                all_products.extend(page_products)

                print(f"Retrieved {len(page_products)} products from page {page}")

                # If this is the last page, break the loop
                if page >= total_pages:
                    break

                # Move to the next page
                page += 1
            else:
                print(f"Error fetching page {page}: {response.status_code} - {response.text}")
                break

        # Save the raw response for reference (just the first page as a sample)
        if all_products:
            with open("sample_res.json", "w") as f:
                json.dump(all_products[:1], f, indent=4)  # Save just one product as a sample

        # Process products for the brochure
        brochure_products = []
        processed_product_ids = set()  # Track processed products to avoid duplicates

        # Get available category images
        img_folder = "img"
        available_images = []
        if os.path.exists(img_folder):
            available_images = [f for f in os.listdir(img_folder) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

        category_mappings = get_category_mappings()

        for product in all_products:
            # Skip if we've already processed this product
            product_id = product.get('id')
            if product_id in processed_product_ids:
                continue
            processed_product_ids.add(product_id)
            # Process categories
            main_category = "Uncategorized"
            sub_category = None
            category_image = None

            # Known subcategories (both Arabic and English)
            subcategory_names = ["Pre-packed", "Loose Item", "معبأ", "سائب"]

            if product['categories']:
                # Check if any of the categories is a known subcategory
                sub_cat_found = False
                for category in product['categories']:
                    if category['name'] in subcategory_names:
                        sub_category = category['name']
                        sub_cat_found = True
                        break

                # If we found a subcategory, the main category is one of the others
                if sub_cat_found and len(product['categories']) > 1:
                    for category in product['categories']:
                        if category['name'] not in subcategory_names:
                            main_category = category['name']
                            break
                # If no subcategory was found, use the first category as main
                elif not sub_cat_found and product['categories']:
                    main_category = product['categories'][0]['name']

            # Translate category and subcategory to target language
            translated_main_category = translate_category_to_language(main_category, language)
            translated_sub_category = translate_category_to_language(sub_category, language) if sub_category else None

            # Find category image using mapping or fuzzy matching (use original category for image lookup)
            main_category_lower = main_category.lower()
            if main_category_lower in category_mappings:
                category_image = category_mappings[main_category_lower]
            else:
                category_image = fuzzy_match_category_to_image(main_category, available_images)

            # Get image URL
            image_url = product['images'][0]['src'] if product['images'] else ""

            # Extract metadata based on language preference
            packaging = get_meta_value(product['meta_data'], 'packaging')
            product_weight = get_meta_value(product['meta_data'], 'product_weight')

            # Get language-specific fields
            if language.lower() == "arabic":
                product_name = get_meta_value(product['meta_data'], 'product_name_arabic') or product['name']
                brand_name = get_meta_value(product['meta_data'], 'brand_name_arabic') or get_meta_value(product['meta_data'], 'brand_name')
                country_of_origin = get_meta_value(product['meta_data'], 'country_of_origin_arabic') or get_meta_value(product['meta_data'], 'country_of_origin')
            else:
                product_name = get_meta_value(product['meta_data'], 'product_name') or product['name']
                brand_name = get_meta_value(product['meta_data'], 'brand_name')
                country_of_origin = get_meta_value(product['meta_data'], 'country_of_origin')

            # Create a product entry for the brochure
            brochure_product = {
                "name": product_name,
                "weight": product_weight or "N/A",
                "packaging": packaging or "N/A",
                "country_of_origin": country_of_origin or "N/A",
                "brand": brand_name or "N/A",
                "image_url": image_url,
                "category": translated_main_category,  # Use translated category
                "subcategory": translated_sub_category,  # Use translated subcategory
                "category_image": category_image,
                "is_packed": sub_category in ["Pre-packed", "معبأ"] if sub_category else False,
                "original_category": main_category,  # Keep original for debugging/reference
                "original_subcategory": sub_category  # Keep original for debugging/reference
            }

            brochure_products.append(brochure_product)

        # Sort products according to hierarchy: packed → category → brand
        def sort_key(product):
            # Primary sort: packed items first (1 for packed, 2 for loose/others)
            packed_priority = 1 if product['is_packed'] else 2
            # Secondary sort: category name
            category = product['category'] or "ZZZ"  # Put uncategorized at end
            # Tertiary sort: brand name
            brand = product['brand'] or "ZZZ"  # Put unknown brands at end
            return (packed_priority, category, brand)

        brochure_products.sort(key=sort_key)

        # Print category summary for debugging
        categories_found = {}
        for product in brochure_products:
            category = product['category']
            categories_found[category] = categories_found.get(category, 0) + 1

        print(f"Processed {len(brochure_products)} products for the {language} brochure")
        print(f"Categories found: {list(categories_found.keys())}")
        for category, count in categories_found.items():
            print(f"  - {category}: {count} products")

        return brochure_products

    except Exception as e:
        print(f"Error processing products: {e}")
        traceback.print_exc()  # Print the full traceback for debugging
        return []

def generate_brochure(output_file=None, language="arabic"):
    """
    Generate the brochure using the fetched product data.

    Args:
        output_file (str): Path for the output PDF file
        language (str): Language for the brochure - "arabic" or "english"
    """
    try:
        print(f"Fetching and processing products for {language} brochure...")
        products = fetch_and_process_products(language=language)

        if products:
            print(f"Found {len(products)} products, generating brochure...")
            if output_file is None:
                # Use the current directory if no output file is specified
                lang_suffix = "_AR" if language.lower() == "arabic" else "_EN"
                output_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), f"Product_Brochure{lang_suffix}.pdf")
            create_brochure(output_file, products, language=language)
            print(f"Brochure generated successfully: {output_file}")
            return True
        else:
            print("No products available to generate brochure")
            return False
    except Exception as e:
        print(f"Error in generate_brochure: {e}")
        traceback.print_exc()
        return False

# Run the brochure generation
if __name__ == "__main__":
    generate_brochure()