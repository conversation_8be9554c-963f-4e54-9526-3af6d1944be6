from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import Paragraph
import os
import requests
from PIL import Image


# Font Registration
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
from reportlab.lib.colors import black, white

fonts_path = "fonts/"
pdfmetrics.registerFont(TTFont("Satoshi-Regular", os.path.join(fonts_path, "Satoshi-Regular.ttf")))
pdfmetrics.registerFont(TTFont("Satoshi-Bold", os.path.join(fonts_path, "Satoshi-Bold.ttf")))

# Try to register Arabic fonts if available
try:
    # Common Arabic font paths - adjust as needed
    arabic_fonts = [
        "fonts/NotoSansArabic-Regular.ttf",
        "fonts/Arial-Unicode.ttf",
        "fonts/Tahoma.ttf",
        "C:/Windows/Fonts/tahoma.ttf",  # Windows system font
        "/System/Library/Fonts/Arial Unicode MS.ttf"  # macOS system font
    ]

    arabic_font_registered = False
    for font_path in arabic_fonts:
        if os.path.exists(font_path):
            try:
                pdfmetrics.registerFont(TTFont("Arabic-Regular", font_path))
                arabic_font_registered = True
                print(f"Arabic font registered: {font_path}")
                break
            except Exception as e:
                print(f"Failed to register Arabic font {font_path}: {e}")
                continue

    if not arabic_font_registered:
        print("Warning: No Arabic font found. Arabic text may not display correctly.")
        # Fallback to default font
        pdfmetrics.registerFont(TTFont("Arabic-Regular", os.path.join(fonts_path, "Satoshi-Regular.ttf")))

except Exception as e:
    print(f"Error setting up Arabic fonts: {e}")
    # Fallback to default font
    pdfmetrics.registerFont(TTFont("Arabic-Regular", os.path.join(fonts_path, "Satoshi-Regular.ttf")))


def download_image(url, save_dir="cached_images"):
    """Download and cache images locally."""
    if not url:
        print("No image URL provided")
        return None

    # Create the cache directory if it doesn't exist
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # Clean the URL to create a valid filename
    import re
    from urllib.parse import urlparse

    # Parse the URL and get the path
    parsed_url = urlparse(url)
    path = parsed_url.path

    # Extract the filename from the path and clean it
    base_name = os.path.basename(path)
    # Remove any invalid characters
    clean_name = re.sub(r'[\\/*?:"<>|]', "_", base_name)

    # If the filename is empty or just has an extension, use a default name
    if not clean_name or clean_name.startswith('.'):
        clean_name = f"image_{hash(url) % 10000}.jpg"

    filename = os.path.join(save_dir, clean_name)

    # Download the image if it doesn't exist
    if not os.path.exists(filename):
        try:
            print(f"Downloading image from {url}")
            response = requests.get(url)
            response.raise_for_status()
            with open(filename, 'wb') as f:
                f.write(response.content)
            print(f"Image saved to {filename}")
        except requests.exceptions.RequestException as e:
            print(f"Error downloading image {url}: {e}")
            return None
        except Exception as e:
            print(f"Unexpected error saving image {url}: {e}")
            return None

    return filename


def add_front_pages(c, img_folder="img"):
    """Add front cover pages to the PDF"""
    front_images = []

    # Look for front cover images
    if os.path.exists(img_folder):
        for i in range(1, 6):  # front-1.jpg to front-5.jpg
            front_file = os.path.join(img_folder, f"front-{i}.jpg")
            if os.path.exists(front_file):
                front_images.append(front_file)

    # Add each front image as a full page
    for front_image in front_images:
        try:
            # Get page dimensions
            page_width, page_height = A4

            # Draw the image to fill the entire page
            c.drawImage(front_image, 0, 0, width=page_width, height=page_height, preserveAspectRatio=True, mask='auto')
            c.showPage()
            print(f"Added front page: {front_image}")
        except Exception as e:
            print(f"Error adding front page {front_image}: {e}")


def add_end_page(c, img_folder="img"):
    """Add end page to the PDF"""
    end_file = os.path.join(img_folder, "end.jpg")

    if os.path.exists(end_file):
        try:
            # Get page dimensions
            page_width, page_height = A4

            # Draw the image to fill the entire page
            c.drawImage(end_file, 0, 0, width=page_width, height=page_height, preserveAspectRatio=True, mask='auto')
            c.showPage()
            print(f"Added end page: {end_file}")
        except Exception as e:
            print(f"Error adding end page {end_file}: {e}")
    else:
        print(f"End page not found: {end_file}")


def add_category_cover_page(c, category_name, category_image, img_folder="img"):
    """Add a category cover page if category image is available"""
    if not category_image:
        return False

    category_image_path = os.path.join(img_folder, category_image)

    if os.path.exists(category_image_path):
        try:
            # Get page dimensions
            page_width, page_height = A4

            # Draw the category image to fill the entire page
            c.drawImage(category_image_path, 0, 0, width=page_width, height=page_height, preserveAspectRatio=True, mask='auto')
            c.showPage()
            print(f"Added category cover page for '{category_name}': {category_image_path}")
            return True
        except Exception as e:
            print(f"Error adding category cover page {category_image_path}: {e}")
            return False
    else:
        print(f"Category image not found: {category_image_path}")
        return False


def get_font_for_language(language="arabic"):
    """Get appropriate font based on language"""
    if language.lower() == "arabic":
        return "Arabic-Regular"
    else:
        return "Satoshi-Regular"


def render_product(c, x, y, grid_width, grid_height, product, language="arabic"):
    """Render a single product on the grid."""
    # Render product image
    try:
        image_path = download_image(product['image_url'])
        if image_path and os.path.exists(image_path):
            img_width = grid_width - 10
            img_height = 1.1 * inch
            c.drawImage(image_path, x + 0.1 * inch, y + grid_height - 1.2 * inch, width=img_width, height=img_height, preserveAspectRatio=True, mask='auto')
        else:
            # Draw a placeholder rectangle if image can't be loaded
            c.setFillColorRGB(0.9, 0.9, 0.9)  # Light gray
            c.rect(x + 0.1 * inch, y + grid_height - 1.2 * inch, grid_width - 10, 1.1 * inch, fill=1, stroke=0)
            c.setFillColorRGB(0.5, 0.5, 0.5)  # Darker gray for text
            c.setFont("Satoshi-Regular", 8)
            c.drawCentredString(x + grid_width/2, y + grid_height - 0.7 * inch, "No Image")
    except Exception as e:
        print(f"Error rendering image for {product['name']}: {e}")
        # Draw a placeholder rectangle if there's an error
        c.setFillColorRGB(0.9, 0.9, 0.9)  # Light gray
        c.rect(x + 0.1 * inch, y + grid_height - 1.2 * inch, grid_width - 10, 1.1 * inch, fill=1, stroke=0)
        c.setFillColorRGB(0.5, 0.5, 0.5)  # Darker gray for text
        c.setFont("Satoshi-Regular", 8)
        c.drawCentredString(x + grid_width/2, y + grid_height - 0.7 * inch, "Image Error")

    # Weight display removed - no longer showing weight circle

    # Packaging circle
    c.setFillColorRGB(0.902, 0.906, 0.914)
    c.circle(x + 0.35 * inch, y + 1.14 * inch, 0.18 * inch, fill=1, stroke=0)
    c.setFillColorRGB(0, 0, 0)
    c.setFont("Satoshi-Regular", 8)
    c.drawCentredString(x + 0.35 * inch, y + grid_height - 0.55 - 0.98 * inch, "Pkg")
    c.drawCentredString(x + 0.35 * inch, y + grid_height - 0.7 - 1.1 * inch, product['packaging'])

    # Product Name
    styles = getSampleStyleSheet()
    style = styles["Normal"]
    style.alignment = 1
    font_name = get_font_for_language(language)
    style.fontName = font_name
    style.fontSize = 9
    para = Paragraph(product['name'], style)
    text_width = grid_width - 0.4 * inch
    para_width, para_height = para.wrap(text_width, y)
    para.drawOn(c, x + (grid_width - text_width) / 2, y + grid_height - 1.2 * inch - para_height)

    # Country of Origin - changed background color to red
    country_box_y = y + grid_height - 1.2 * inch - para_height - 0.2 * inch
    c.setFillColorRGB(0.925, 0.129, 0.165)  # Red color (same as category header)
    country_text_width = c.stringWidth(product['country_of_origin'], font_name, 8) + 0.1 * inch
    c.roundRect(x + grid_width / 2 - country_text_width / 2, country_box_y, country_text_width, 0.15 * inch, 0.02 * inch, fill=1, stroke=0)
    c.setFillColorRGB(1, 1, 1)
    c.setFont(font_name, 8)
    c.drawCentredString(x + grid_width / 2, country_box_y + 0.04 * inch, product['country_of_origin'])


def render_category_header(c, category_name, page_width, page_height, margin_y, language="arabic"):
    """Render the main category header with flat left side."""
    font_name = get_font_for_language(language)
    text_width = c.stringWidth(category_name, font_name, 13) + 1 * inch  # Add padding
    x = 0
    y = page_height - margin_y - 0.8 * inch
    height = 0.4 * inch
    radius = 0.2 * inch

    # Draw category background with rounded corners only on the right side
    # First draw the main rounded rectangle
    c.setFillColorRGB(0.925, 0.129, 0.165)  # Red color
    c.roundRect(x, y, text_width, height, radius, stroke=0, fill=1)

    # Then cover the left rounded corners with a rectangle to make it flat on the left
    c.setFillColorRGB(0.925, 0.129, 0.165)  # Same red color
    c.rect(x, y, radius, height, stroke=0, fill=1)  # Rectangle covering the left rounded corners

    # Add text
    c.setFillColorRGB(1, 1, 1)  # White text color
    c.setFont(font_name, 13)
    c.drawString(x + 0.7 * inch, y + 0.15 * inch, category_name)


def render_subcategory_header(c, subcategory_name, page_width, page_height, margin_y, language="arabic"):
    """Render the subcategory header below the main category."""
    if not subcategory_name:
        return

    font_name = get_font_for_language(language)
    text_width = c.stringWidth(subcategory_name, font_name, 11) + 0.8 * inch  # Add padding
    x = 0.5 * inch  # Indent from the left edge
    y = page_height - margin_y - 1.3 * inch  # Position below the main category header
    height = 0.3 * inch
    radius = 0.15 * inch

    # Draw subcategory background
    c.setFillColorRGB(0.2, 0.2, 0.2)  # Dark gray color
    c.roundRect(x, y, text_width, height, radius, stroke=0, fill=1)

    # Add text
    c.setFillColorRGB(1, 1, 1)  # White text color
    c.setFont(font_name, 11)
    c.drawString(x + 0.4 * inch, y + 0.1 * inch, subcategory_name)


def generate_page(c, products, category_name, subcategory_name, margin_x, margin_y, grid_width, grid_height, page_width, page_height, language="arabic"):
    """Generate a single page with category and subcategory headers."""
    # Render the main category header
    render_category_header(c, category_name, page_width, page_height, margin_y, language)

    # Render the subcategory header if provided
    if subcategory_name:
        render_subcategory_header(c, subcategory_name, page_width, page_height, margin_y, language)

    # Adjust the starting position for products based on whether there's a subcategory
    y_offset = 1.5 * inch if not subcategory_name else 2.0 * inch

    for index, product in enumerate(products):
        row = index // 4
        col = index % 4
        x = margin_x + col * grid_width
        y = page_height - margin_y - (row + 1) * grid_height - y_offset
        render_product(c, x, y, grid_width, grid_height, product, language)


def create_brochure(output_file, products, language="arabic"):
    """Generate the entire brochure grouped by category and subcategory."""
    c = canvas.Canvas(output_file, pagesize=A4)
    margin_x = 0.5 * inch
    margin_y = 1 * inch
    grid_width = (A4[0] - 2 * margin_x) / 4

    # Adjust grid height based on whether there's a subcategory (which takes up more vertical space)
    base_grid_height = ((A4[1] - 2 * margin_y - 1.5 * inch) / 4) + 0.1 * inch

    # Add front cover pages
    add_front_pages(c)

    # First, group products by main category
    main_categories = {}
    for product in products:
        main_categories.setdefault(product['category'], []).append(product)

    # Track which categories have had cover pages added
    category_covers_added = set()

    # For each main category, further group by subcategory
    for main_category, main_category_products in main_categories.items():
        # Add category cover page if not already added and image is available
        if main_category not in category_covers_added:
            # Get category image from any product in this category
            category_image = None
            for product in main_category_products:
                if product.get('category_image'):
                    category_image = product['category_image']
                    break

            if category_image:
                add_category_cover_page(c, main_category, category_image)
                category_covers_added.add(main_category)

        # Group by subcategory
        subcategories = {}
        for product in main_category_products:
            subcategory = product.get('subcategory')
            # Use None as key for products without subcategory
            subcategories.setdefault(subcategory, []).append(product)

        # Process each subcategory
        for subcategory, subcategory_products in subcategories.items():
            # Calculate how many pages we need for this subcategory
            products_per_page = 16  # 4x4 grid

            # Generate pages for this subcategory
            for page_start in range(0, len(subcategory_products), products_per_page):
                page_products = subcategory_products[page_start:page_start + products_per_page]

                # Generate the page with appropriate headers
                generate_page(c, page_products, main_category, subcategory,
                             margin_x, margin_y, grid_width, base_grid_height, A4[0], A4[1], language)

                c.showPage()

    # Add end page
    add_end_page(c)

    c.save()


# Example usage
products = [
    {"name": "Dehydrated Tropical Mix", "weight": "250g", "packaging": "24", "country_of_origin": "Thailand", "image_url": "https://auraquill.com/wp-content/uploads/2024/11/Grateful-Vibes-1.png", "category": "Dried Fruits"},
    {"name": "Dehydrated Mango", "weight": "200g", "packaging": "20", "country_of_origin": "India", "image_url": "https://auraquill.com/wp-content/uploads/2024/11/Grateful-Vibes-1.png", "category": "Dried Fruits"},
    {"name": "Assorted Japanese Crackers", "weight": "150g", "packaging": "12", "country_of_origin": "Japan", "image_url": "https://auraquill.com/wp-content/uploads/2024/11/Grateful-Vibes-1.png", "category": "Snacks"},
    # Add more products with different categories
]

output_file = "D:/Emilda/IDTC/brochure/Styled_Brochure_Template.pdf"
create_brochure(output_file, products)
